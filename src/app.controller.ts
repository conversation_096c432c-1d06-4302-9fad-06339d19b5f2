import { <PERSON>, Get, Logger, Sse } from '@nestjs/common';
import { AppService } from './app.service';
import { Observable } from 'rxjs';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}
  private logger = new Logger();
  @Get()
  getHello(): string {
    this.logger.log('getHello', AppController.name);
    return this.appService.getHello();
  }
  @Sse('stream')
  stream() {
    return new Observable((observer) => {
      observer.next({ event: 'message', data: 'Hello World!' });
      setTimeout(() => {
        observer.next({ event: 'message', data: 'Hello World!' });
      }, 1000);
      setTimeout(() => {
        observer.next({ event: 'message', data: 'Hello World!2' });
        observer.complete();
      }, 2000);
    })
  }
}
