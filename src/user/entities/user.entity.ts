import { SessionHistory } from "src/session_history/entities/session_history.entity";
import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedColumn, OneToMany } from "typeorm";

@Entity()
export class User {
  @PrimaryGeneratedColumn()
  id: number
  @Column()
  username: string;

  @Column()
  password: string;

  @Column()
  phoneNumber: string;

  @OneToMany(() => SessionHistory, (sessionHistory) => sessionHistory.user)
  sessionHistory: SessionHistory[];


}
