import { Injectable } from '@nestjs/common';
import { CreateSessionHistoryDto } from './dto/create-session_history.dto';
import { UpdateSessionHistoryDto } from './dto/update-session_history.dto';

@Injectable()
export class SessionHistoryService {
  create(createSessionHistoryDto: CreateSessionHistoryDto) {
    return 'This action adds a new sessionHistory';
  }

  findAll() {
    return `This action returns all sessionHistory`;
  }

  findOne(id: number) {
    return `This action returns a #${id} sessionHistory`;
  }

  update(id: number, updateSessionHistoryDto: UpdateSessionHistoryDto) {
    return `This action updates a #${id} sessionHistory`;
  }

  remove(id: number) {
    return `This action removes a #${id} sessionHistory`;
  }
}
