import { <PERSON>, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { SessionHistoryService } from './session_history.service';
import { CreateSessionHistoryDto } from './dto/create-session_history.dto';
import { UpdateSessionHistoryDto } from './dto/update-session_history.dto';

@Controller('session-history')
export class SessionHistoryController {
  constructor(private readonly sessionHistoryService: SessionHistoryService) {}

  @Post()
  create(@Body() createSessionHistoryDto: CreateSessionHistoryDto) {
    return this.sessionHistoryService.create(createSessionHistoryDto);
  }

  @Get()
  findAll() {
    return this.sessionHistoryService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.sessionHistoryService.findOne(+id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateSessionHistoryDto: UpdateSessionHistoryDto) {
    return this.sessionHistoryService.update(+id, updateSessionHistoryDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.sessionHistoryService.remove(+id);
  }
}
