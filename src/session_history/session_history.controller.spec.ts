import { Test, TestingModule } from '@nestjs/testing';
import { SessionHistoryController } from './session_history.controller';
import { SessionHistoryService } from './session_history.service';

describe('SessionHistoryController', () => {
  let controller: SessionHistoryController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [SessionHistoryController],
      providers: [SessionHistoryService],
    }).compile();

    controller = module.get<SessionHistoryController>(SessionHistoryController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
