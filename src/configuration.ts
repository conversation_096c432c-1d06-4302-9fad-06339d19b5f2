import * as yaml from 'js-yaml';
import * as fs from 'fs';
import { join } from 'path';

const YAML_CONFIG_FILENAME = 'config.yml'
console.log('__dirname',__dirname)
export default () => {
  const config = yaml.load(fs.readFileSync(join(__dirname, '../config',YAML_CONFIG_FILENAME), 'utf8')) as Record<string, any>;
  if (config.http.port < 1024 || config.http.port > 49151) {
    throw new Error('HTTP port must be between 1024 and 49151');
  }
  return config;
}
